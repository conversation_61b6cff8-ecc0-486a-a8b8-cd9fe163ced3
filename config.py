"""
Configuration settings for PDF to Markdown Converter
"""

# Claude API Settings
CLAUDE_MODELS = {
    # Claude Opus 4.1 - Most capable
    "claude-opus-4-1-20250805": {
        "name": "Claude Opus 4.1",
        "description": "Most capable model with advanced reasoning",
        "max_tokens": 32000,
        "speed": "Slower",
        "cost": "Very High",
        "context_window": 200000
    },
    
    # Claude Opus 4
    "claude-opus-4-20250514": {
        "name": "Claude Opus 4",
        "description": "Powerful model for complex tasks",
        "max_tokens": 32000,
        "speed": "Slower",
        "cost": "Very High",
        "context_window": 200000
    },
    
    # Claude Sonnet 4
    "claude-sonnet-4-20250514": {
        "name": "Claude Sonnet 4",
        "description": "High performance with extended output",
        "max_tokens": 64000,
        "speed": "Fast",
        "cost": "High",
        "context_window": 200000
    },
    
    # Claude 3.7 Sonnet
    "claude-3-7-sonnet-20250219": {
        "name": "Claude 3.7 Sonnet",
        "description": "Hybrid model with flexible reasoning",
        "max_tokens": 64000,
        "speed": "Fast",
        "cost": "High",
        "context_window": 200000
    },
    
    # <PERSON> 3.5 Sonnet (Latest v2)
    "claude-3-5-sonnet-20241022": {
        "name": "Claude 3.5 Sonnet v2",
        "description": "Balanced performance and quality",
        "max_tokens": 8192,
        "speed": "Fast",
        "cost": "Medium",
        "context_window": 200000
    },
    
    # Claude 3.5 Haiku
    "claude-3-5-haiku-20241022": {
        "name": "Claude 3.5 Haiku",
        "description": "Fastest and most cost-effective",
        "max_tokens": 8192,
        "speed": "Very Fast",
        "cost": "Low",
        "context_window": 200000
    },
    
    # Claude 3 Haiku (Legacy)
    "claude-3-haiku-20240307": {
        "name": "Claude 3 Haiku",
        "description": "Light, fast, efficient (legacy)",
        "max_tokens": 4096,
        "speed": "Very Fast",
        "cost": "Very Low",
        "context_window": 200000
    }
}

# Default model - Claude 3.5 Haiku for best speed/cost ratio
DEFAULT_MODEL = "claude-3-5-haiku-20241022"
RATE_LIMIT_DELAY = 1.0  # seconds between API calls

# PDF Processing Settings
DEFAULT_DPI = 200  # DPI for PDF to image conversion
IMAGE_FORMAT = "PNG"

# UI Settings
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
LOG_HEIGHT = 8

# File Settings
SUPPORTED_EXTENSIONS = [".pdf"]
OUTPUT_EXTENSION = ".md"

# Conversion Prompt
CONVERSION_PROMPT = """Convert this PDF page to clean, well-formatted Markdown. 
Please:
1. Preserve all text content accurately
2. Use proper Markdown formatting (headers, lists, tables, etc.)
3. Maintain the document structure and hierarchy
4. Format any code blocks with appropriate syntax highlighting
5. Convert any tables to Markdown table format
6. Ignore watermarks, page numbers, and irrelevant artifacts
7. If there are mathematical formulas, convert them to LaTeX format within $ delimiters
8. Preserve any bullet points and numbered lists with proper indentation

Return ONLY the Markdown content, no explanations or additional text."""