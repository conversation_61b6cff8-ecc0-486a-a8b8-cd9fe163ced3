#!/usr/bin/env python3
"""
Launcher for PDF to Markdown Converter.
Directly launches the main application.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if all dependencies are installed."""
    try:
        # Run the test installation script
        result = subprocess.run([sys.executable, "test_installation.py"], 
                              capture_output=True, text=True)
        return result.returncode == 0, result.stdout
    except Exception as e:
        return False, str(e)

def main():
    """Main launcher function."""
    print("PDF to Markdown Converter - Launcher")
    print("="*50)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("Error: main.py not found!")
        print("Please run this script from the project directory.")
        input("Press Enter to exit...")
        return
    
    # Check dependencies
    print("Checking dependencies...")
    deps_ok, deps_output = check_dependencies()
    
    if not deps_ok:
        print("❌ Dependencies check failed!")
        print("Please run: python setup.py")
        print("\nDependency check output:")
        print(deps_output)
        input("Press Enter to exit...")
        return
    
    print("✅ All dependencies are installed correctly!")
    print("\nLaunching PDF to Markdown Converter...")
    
    # Launch the main application
    try:
        subprocess.run([sys.executable, "main.py"])
    except Exception as e:
        print(f"Error launching application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nLauncher cancelled by user.")
    except Exception as e:
        print(f"\nError: {e}")
        input("Press Enter to exit...")