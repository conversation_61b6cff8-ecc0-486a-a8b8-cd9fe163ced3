#!/usr/bin/env python3
"""
Launcher script that checks dependencies and runs the PDF to Markdown converter.
"""

import sys
import subprocess
import os

def check_dependencies():
    """Check if all required dependencies are installed."""
    missing_deps = []
    
    # Check Python modules
    modules = [
        "anthropic",
        "PyPDF2", 
        "pdf2image",
        "PIL",
        "tkinter"
    ]
    
    for module in modules:
        try:
            __import__(module)
        except ImportError:
            missing_deps.append(module)
    
    # Check poppler
    try:
        subprocess.run(["pdftoppm", "-h"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        missing_deps.append("poppler")
    
    return missing_deps

def install_python_deps():
    """Install Python dependencies."""
    print("Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main launcher function."""
    print("PDF to Markdown Converter Launcher")
    print("=" * 40)
    
    # Check dependencies
    missing = check_dependencies()
    
    if missing:
        print("Missing dependencies:")
        for dep in missing:
            print(f"  - {dep}")
        
        if "poppler" in missing:
            print("\nPoppler installation required!")
            print("Please run: python setup.py")
            return False
        
        # Try to install Python dependencies
        python_deps = [dep for dep in missing if dep != "poppler"]
        if python_deps:
            print("\nAttempting to install Python dependencies...")
            if install_python_deps():
                print("Dependencies installed successfully!")
            else:
                print("Failed to install dependencies. Please run: pip install -r requirements.txt")
                return False
    
    # Check if main file exists
    if not os.path.exists("main.py"):
        print("Error: main.py not found!")
        return False
    
    # Launch the application
    print("Launching PDF to Markdown Converter...")
    try:
        # Import and run the main module
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "main.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        main_module.main()
    except Exception as e:
        print(f"Error launching application: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
