#!/usr/bin/env python3
"""
Setup script for PDF to Markdown Converter
"""

import subprocess
import sys
import os
import platform

def install_requirements():
    """Install Python requirements."""
    print("Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install Python dependencies: {e}")
        return False

def check_poppler():
    """Check if poppler is installed."""
    try:
        subprocess.run(["pdftoppm", "-h"], capture_output=True, check=True)
        print("✓ Poppler is already installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ Poppler is not installed or not in PATH")
        return False

def install_poppler_instructions():
    """Provide instructions for installing poppler."""
    system = platform.system().lower()
    
    print("\n" + "="*50)
    print("POPPLER INSTALLATION REQUIRED")
    print("="*50)
    
    if system == "windows":
        print("For Windows:")
        print("1. Download poppler from: https://github.com/oschwartz10612/poppler-windows/releases/")
        print("2. Extract the files")
        print("3. Add the 'bin' folder to your system PATH")
        print("4. Restart your command prompt/terminal")
        
    elif system == "darwin":  # macOS
        print("For macOS:")
        print("Run: brew install poppler")
        print("(Make sure you have Homebrew installed)")
        
    elif system == "linux":
        print("For Linux (Ubuntu/Debian):")
        print("Run: sudo apt-get install poppler-utils")
        print("\nFor other Linux distributions:")
        print("Use your package manager to install poppler-utils")
    
    print("\nAfter installing poppler, run this setup script again.")
    print("="*50)

def create_sample_folders():
    """Create sample input and output folders."""
    folders = ["sample_input", "sample_output"]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"✓ Created {folder} folder")

def main():
    """Main setup function."""
    print("PDF to Markdown Converter Setup")
    print("="*40)
    
    # Install Python requirements
    if not install_requirements():
        print("Setup failed. Please check the error messages above.")
        return False
    
    # Check for poppler
    if not check_poppler():
        install_poppler_instructions()
        return False
    
    # Create sample folders
    create_sample_folders()
    
    print("\n" + "="*40)
    print("✓ Setup completed successfully!")
    print("="*40)
    print("\nTo run the application:")
    print("python main.py")
    print("\nDon't forget to:")
    print("1. Get your Anthropic API key from: https://console.anthropic.com/")
    print("2. Place your PDF files in the input folder")
    print("3. Select your input and output folders in the application")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
