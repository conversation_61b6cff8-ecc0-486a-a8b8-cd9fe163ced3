"""
Secure storage for API keys using Windows Credential Manager or encrypted local storage.
"""

import os
import sys
import json
import base64
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import getpass

# Try to import Windows credential storage
try:
    import keyring
    KEYRING_AVAILABLE = True
except ImportError:
    KEYRING_AVAILABLE = False

class SecureStorage:
    """Secure storage for API keys and sensitive data."""
    
    def __init__(self, app_name="PDFToMarkdownConverter"):
        self.app_name = app_name
        self.config_dir = Path.home() / ".pdf_to_markdown"
        self.config_dir.mkdir(exist_ok=True)
        self.key_file = self.config_dir / "key.enc"
        self.salt_file = self.config_dir / "salt.dat"
        
    def _get_machine_key(self):
        """Generate a machine-specific key for encryption."""
        # Use machine-specific information to generate a consistent key
        machine_info = f"{os.environ.get('COMPUTERNAME', 'default')}{os.environ.get('USERNAME', 'user')}"
        return machine_info.encode()
    
    def _derive_key(self, password: bytes, salt: bytes) -> bytes:
        """Derive encryption key from password and salt."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password))
    
    def _get_or_create_salt(self) -> bytes:
        """Get existing salt or create new one."""
        if self.salt_file.exists():
            return self.salt_file.read_bytes()
        else:
            salt = os.urandom(16)
            self.salt_file.write_bytes(salt)
            return salt
    
    def save_api_key(self, api_key: str) -> bool:
        """Save API key securely."""
        try:
            if KEYRING_AVAILABLE:
                # Use Windows Credential Manager if available
                keyring.set_password(self.app_name, "anthropic_api_key", api_key)
                return True
            else:
                # Use encrypted local storage
                salt = self._get_or_create_salt()
                machine_key = self._get_machine_key()
                key = self._derive_key(machine_key, salt)
                
                fernet = Fernet(key)
                encrypted_key = fernet.encrypt(api_key.encode())
                
                self.key_file.write_bytes(encrypted_key)
                return True
                
        except Exception as e:
            print(f"Error saving API key: {e}")
            return False
    
    def load_api_key(self) -> str:
        """Load API key securely."""
        try:
            if KEYRING_AVAILABLE:
                # Try Windows Credential Manager first
                api_key = keyring.get_password(self.app_name, "anthropic_api_key")
                if api_key:
                    return api_key
            
            # Try encrypted local storage
            if self.key_file.exists() and self.salt_file.exists():
                salt = self.salt_file.read_bytes()
                machine_key = self._get_machine_key()
                key = self._derive_key(machine_key, salt)
                
                fernet = Fernet(key)
                encrypted_key = self.key_file.read_bytes()
                decrypted_key = fernet.decrypt(encrypted_key)
                
                return decrypted_key.decode()
                
        except Exception as e:
            print(f"Error loading API key: {e}")
            
        return ""
    
    def delete_api_key(self) -> bool:
        """Delete stored API key."""
        try:
            if KEYRING_AVAILABLE:
                keyring.delete_password(self.app_name, "anthropic_api_key")
            
            if self.key_file.exists():
                self.key_file.unlink()
            
            return True
            
        except Exception as e:
            print(f"Error deleting API key: {e}")
            return False
    
    def has_stored_key(self) -> bool:
        """Check if an API key is stored."""
        try:
            if KEYRING_AVAILABLE:
                api_key = keyring.get_password(self.app_name, "anthropic_api_key")
                if api_key:
                    return True
            
            return self.key_file.exists() and self.salt_file.exists()
            
        except Exception:
            return False

def test_secure_storage():
    """Test the secure storage functionality."""
    storage = SecureStorage()
    
    print("Testing secure storage...")
    
    # Test saving
    test_key = "test-api-key-12345"
    if storage.save_api_key(test_key):
        print("✓ API key saved successfully")
    else:
        print("✗ Failed to save API key")
        return
    
    # Test loading
    loaded_key = storage.load_api_key()
    if loaded_key == test_key:
        print("✓ API key loaded successfully")
    else:
        print("✗ Failed to load API key correctly")
        return
    
    # Test deletion
    if storage.delete_api_key():
        print("✓ API key deleted successfully")
    else:
        print("✗ Failed to delete API key")
        return
    
    # Verify deletion
    if not storage.has_stored_key():
        print("✓ API key properly removed")
    else:
        print("✗ API key still exists after deletion")
    
    print("All tests passed!")

if __name__ == "__main__":
    test_secure_storage()
