"""
Performance optimizations for PDF to Markdown converter.
"""

import asyncio
import aiohttp
import concurrent.futures
import multiprocessing
from typing import List, Dict, Tuple, Optional
import time
import threading
from queue import Queue
import gc
from pathlib import Path
import io
import base64
from PIL import Image
import anthropic

class OptimizedPDFProcessor:
    """Optimized PDF processor with parallel processing and async capabilities."""
    
    def __init__(self, api_key: str, max_workers: int = None, batch_size: int = 3, model: str = None):
        """
        Initialize optimized processor.
        
        Args:
            api_key: Anthropic API key
            max_workers: Maximum number of worker threads (default: CPU count)
            batch_size: Number of pages to process in parallel
            model: Claude model to use (default from config)
        """
        self.api_key = api_key
        self.max_workers = max_workers or min(multiprocessing.cpu_count(), 4)
        self.batch_size = batch_size
        self.client = anthropic.Anthropic(api_key=api_key)
        
        # Import config here to avoid circular imports
        import config
        self.model = model or config.DEFAULT_MODEL
        self.max_tokens = config.CLAUDE_MODELS.get(self.model, {}).get('max_tokens', 8000)
        
        # Performance tracking
        self.stats = {
            'total_pages': 0,
            'processing_time': 0,
            'api_calls': 0
        }
    
    def optimize_image(self, image: Image.Image, quality: int = 85, max_size: Tuple[int, int] = (2048, 2048)) -> str:
        """
        Optimize image for faster processing and smaller payload.
        
        Args:
            image: PIL Image object
            quality: JPEG quality (1-100)
            max_size: Maximum dimensions (width, height)
            
        Returns:
            Base64 encoded optimized image
        """
        # Resize if too large
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Convert to RGB if necessary (for JPEG)
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
            image = background
        
        # Compress to JPEG for smaller size
        buffered = io.BytesIO()
        image.save(buffered, format="JPEG", quality=quality, optimize=True)
        
        return base64.b64encode(buffered.getvalue()).decode('utf-8')
    
    async def process_page_async(self, session: aiohttp.ClientSession, image_base64: str, page_num: int) -> Tuple[int, str]:
        """
        Process a single page asynchronously.
        
        Args:
            session: aiohttp session
            image_base64: Base64 encoded image
            page_num: Page number for ordering
            
        Returns:
            Tuple of (page_number, markdown_content)
        """
        # No caching - process each page fresh
        
        try:
            # Prepare the request
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01"
            }
            
            payload = {
                "model": self.model,
                "max_tokens": self.max_tokens,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/jpeg",
                                    "data": image_base64
                                }
                            },
                            {
                                "type": "text",
                                "text": """Convert this PDF page to clean, well-formatted Markdown. 
Please:
1. Preserve all text content accurately
2. Use proper Markdown formatting (headers, lists, tables, etc.)
3. Maintain the document structure and hierarchy
4. Format any code blocks with appropriate syntax highlighting
5. Convert any tables to Markdown table format
6. Ignore watermarks, page numbers, and irrelevant artifacts
7. If there are mathematical formulas, convert them to LaTeX format within $ delimiters
8. Preserve any bullet points and numbered lists with proper indentation

Return ONLY the Markdown content, no explanations or additional text."""
                            }
                        ]
                    }
                ]
            }
            
            # Make async API call
            async with session.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['content'][0]['text']



                    self.stats['api_calls'] += 1
                    return page_num, content
                else:
                    error_text = await response.text()
                    raise Exception(f"API error {response.status}: {error_text}")
                    
        except Exception as e:
            return page_num, f"Error processing page {page_num}: {str(e)}"
    
    async def process_pages_batch_async(self, images: List[Tuple[int, Image.Image]]) -> List[Tuple[int, str]]:
        """
        Process a batch of pages asynchronously.
        
        Args:
            images: List of (page_number, PIL_Image) tuples
            
        Returns:
            List of (page_number, markdown_content) tuples
        """
        # Optimize images in parallel using thread pool
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            optimization_tasks = [
                executor.submit(self.optimize_image, img) 
                for _, img in images
            ]
            
            optimized_images = []
            for i, future in enumerate(concurrent.futures.as_completed(optimization_tasks)):
                page_num = images[i][0]
                image_base64 = future.result()
                optimized_images.append((page_num, image_base64))
        
        # Sort by page number to maintain order
        optimized_images.sort(key=lambda x: x[0])
        
        # Process pages asynchronously
        connector = aiohttp.TCPConnector(limit=self.max_workers)
        timeout = aiohttp.ClientTimeout(total=300)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            tasks = [
                self.process_page_async(session, img_b64, page_num)
                for page_num, img_b64 in optimized_images
            ]
            
            # Add delay between requests to respect rate limits
            results = []
            for i, task in enumerate(asyncio.as_completed(tasks)):
                if i > 0:  # Don't delay the first request
                    await asyncio.sleep(0.5)  # 500ms delay between requests
                result = await task
                results.append(result)
            
            return sorted(results, key=lambda x: x[0])  # Sort by page number
    
    def process_pdf_optimized(self, pdf_path: str, output_dir: str, 
                            progress_callback=None, status_callback=None) -> Tuple[bool, str]:
        """
        Process PDF with optimizations.
        
        Args:
            pdf_path: Path to PDF file
            output_dir: Output directory
            progress_callback: Function to call with progress updates
            status_callback: Function to call with status updates
            
        Returns:
            Tuple of (success, message)
        """
        start_time = time.time()
        
        try:
            # Import here to avoid circular imports
            from pdf2image import convert_from_path
            
            if status_callback:
                status_callback("Converting PDF to images...")
            
            # Import config here to avoid circular imports
            import config
            
            # Convert PDF to images with optimized settings
            images = convert_from_path(
                pdf_path, 
                dpi=config.DEFAULT_DPI,  # Use DPI from config
                thread_count=self.max_workers,
                use_pdftocairo=True  # Faster backend if available
            )
            
            if not images:
                return False, "Failed to convert PDF to images"
            
            self.stats['total_pages'] += len(images)
            
            # Prepare images with page numbers
            numbered_images = [(i + 1, img) for i, img in enumerate(images)]
            
            # Process in batches
            all_results = []
            total_batches = (len(numbered_images) + self.batch_size - 1) // self.batch_size
            
            for batch_idx in range(0, len(numbered_images), self.batch_size):
                batch = numbered_images[batch_idx:batch_idx + self.batch_size]
                batch_num = (batch_idx // self.batch_size) + 1
                
                if status_callback:
                    status_callback(f"Processing batch {batch_num}/{total_batches} ({len(batch)} pages)")
                
                # Process batch asynchronously
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    batch_results = loop.run_until_complete(self.process_pages_batch_async(batch))
                    all_results.extend(batch_results)
                finally:
                    loop.close()
                
                if progress_callback:
                    progress = min(100, (len(all_results) / len(numbered_images)) * 100)
                    progress_callback(progress)
                
                # Clean up memory
                for _, img in batch:
                    del img
                gc.collect()
            
            # Sort results by page number and combine
            all_results.sort(key=lambda x: x[0])
            markdown_pages = [result[1] for result in all_results]
            final_markdown = "\n\n---\n\n".join(markdown_pages)
            
            # Save to file
            pdf_name = Path(pdf_path).stem
            output_path = Path(output_dir) / f"{pdf_name}.md"



            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_markdown)
            
            # Update stats - accumulate processing time
            processing_time = time.time() - start_time
            self.stats['processing_time'] += processing_time
            
            return True, f"Successfully converted to {output_path}"
            
        except Exception as e:
            return False, f"Error processing PDF: {str(e)}"
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics."""
        stats = self.stats.copy()
        if stats['processing_time'] > 0:
            stats['pages_per_second'] = stats['total_pages'] / stats['processing_time']
            stats['avg_time_per_page'] = stats['processing_time'] / stats['total_pages']
        return stats
    
    def clear_stats(self):
        """Clear performance statistics."""
        self.stats = {
            'total_pages': 0,
            'processing_time': 0,
            'api_calls': 0
        }
        gc.collect()

# Performance monitoring utilities
class PerformanceMonitor:
    """Monitor and log performance metrics."""
    
    def __init__(self):
        self.metrics = []
    
    def start_timer(self, operation: str) -> str:
        """Start timing an operation."""
        timer_id = f"{operation}_{time.time()}"
        self.metrics.append({
            'id': timer_id,
            'operation': operation,
            'start_time': time.time(),
            'end_time': None,
            'duration': None
        })
        return timer_id
    
    def end_timer(self, timer_id: str):
        """End timing an operation."""
        for metric in self.metrics:
            if metric['id'] == timer_id:
                metric['end_time'] = time.time()
                metric['duration'] = metric['end_time'] - metric['start_time']
                break
    
    def get_summary(self) -> Dict:
        """Get performance summary."""
        completed_metrics = [m for m in self.metrics if m['duration'] is not None]
        
        if not completed_metrics:
            return {}
        
        operations = {}
        for metric in completed_metrics:
            op = metric['operation']
            if op not in operations:
                operations[op] = []
            operations[op].append(metric['duration'])
        
        summary = {}
        for op, durations in operations.items():
            summary[op] = {
                'count': len(durations),
                'total_time': sum(durations),
                'avg_time': sum(durations) / len(durations),
                'min_time': min(durations),
                'max_time': max(durations)
            }
        
        return summary
