@echo off
setlocal enableextensions enabledelayedexpansion

rem ------------------------------------------------------------
rem PDF to Markdown Converter - Windows Launcher
rem - Creates/uses a local virtual environment (.venv)
rem - Installs/updates dependencies when needed
rem - Runs preflight checks
rem - Launches the app
rem ------------------------------------------------------------

echo.
echo ===============================================
echo   Starting PDF to Markdown Converter (Windows)
echo ===============================================
echo.

rem Change to the directory of this script
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

rem 1) Find a Python 3 interpreter
set "PY_CMD="
set "PY_ARGS="
where py >nul 2>&1
if %ERRORLEVEL%==0 (
    set "PY_CMD=py"
    set "PY_ARGS=-3"
    goto :python_found
)
where python >nul 2>&1
if %ERRORLEVEL%==0 (
    set "PY_CMD=python"
    goto :python_found
)
where python3 >nul 2>&1
if %ERRORLEVEL%==0 (
    set "PY_CMD=python3"
    goto :python_found
)

:python_found
if not defined PY_CMD (
    echo Error: Python 3 not found on PATH.
    echo Please install Python 3.10+ from https://www.python.org/downloads/ and try again.
    goto :end
)

echo Using Python: %PY_CMD% %PY_ARGS%

rem 2) Ensure virtual environment exists
set "VENV_DIR=.venv"
set "VENV_PY=%VENV_DIR%\Scripts\python.exe"
set "VENV_PIP=%VENV_DIR%\Scripts\pip.exe"

if not exist "%VENV_PY%" (
    echo Creating virtual environment in %VENV_DIR% ...
    %PY_CMD% %PY_ARGS% -m venv "%VENV_DIR%"
    if errorlevel 1 (
        echo Failed to create virtual environment. Aborting.
        goto :end
    )
)

rem 3) Install/Update Python dependencies (safe to run every time)
set "REQ_FILE=requirements.txt"
if exist "%REQ_FILE%" (
    echo Installing/Updating Python dependencies (this may take a moment)...
    "%VENV_PY%" -m pip install --upgrade pip
    if errorlevel 1 (
        echo Warning: Failed to upgrade pip. Continuing...
    )
    "%VENV_PY%" -m pip install -r "%REQ_FILE%"
    if errorlevel 1 (
        echo.
        echo Failed to install dependencies with pip.
        echo You can try running setup: "%VENV_PY%" setup.py
        goto :end
    )
)

rem 4) Preflight check (will also verify Poppler availability)
echo.
echo Running preflight checks...
"%VENV_PY%" "test_installation.py"
if errorlevel 1 (
    echo.
    echo Some dependencies are missing.
    echo - If Poppler is missing, please follow the instructions in README.md
    echo   (Windows users: install from https://github.com/oschwartz10612/poppler-windows/releases/ ,
    echo    then add its 'bin' folder to your PATH)
    echo.
    echo You can also run setup to help: "%VENV_PY%" setup.py
    goto :end
)

rem 5) Launch the application via the Python launcher script
echo.
echo Launching application...
"%VENV_PY%" "launcher.py"
set "EXIT_CODE=%ERRORLEVEL%"

echo.
if "%EXIT_CODE%"=="0" (
    echo Application exited successfully.
) else (
    echo Application exited with code %EXIT_CODE%.
)

:end

echo.
pause