@echo off
setlocal

rem ------------------------------------------------------------
rem PDF to Markdown Converter - Windows Launcher
rem - Creates/uses a local virtual environment (.venv)
rem - Installs/updates dependencies when needed
rem - Runs preflight checks
rem - Launches the app
rem ------------------------------------------------------------

echo.
echo ===============================================
echo   Starting PDF to Markdown Converter (Windows)
echo ===============================================
echo.

rem Change to the directory of this script
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

rem Deactivate any existing virtual environment
if defined VIRTUAL_ENV (
    echo Deactivating existing virtual environment...
    call deactivate 2>nul
)

rem 1) Find a Python 3 interpreter
set "PYTHON_EXE="

rem Try py launcher first
where py >nul 2>&1
if %ERRORLEVEL%==0 (
    echo Found Python launcher
    py -3 --version >nul 2>&1
    if %ERRORLEVEL%==0 (
        set "PYTHON_EXE=py -3"
        goto :python_found
    )
)

rem Try python command
where python >nul 2>&1
if %ERRORLEVEL%==0 (
    python --version 2>&1 | find "Python 3" >nul
    if %ERRORLEVEL%==0 (
        set "PYTHON_EXE=python"
        goto :python_found
    )
)

rem Try python3 command
where python3 >nul 2>&1
if %ERRORLEVEL%==0 (
    set "PYTHON_EXE=python3"
    goto :python_found
)

echo Error: Python 3 not found on PATH.
echo Please install Python 3.10+ from https://www.python.org/downloads/ and try again.
goto :end

:python_found
echo Using Python: %PYTHON_EXE%

rem 2) Ensure virtual environment exists
set "VENV_DIR=.venv"
set "VENV_PY=%VENV_DIR%\Scripts\python.exe"

if not exist "%VENV_PY%" (
    echo Creating virtual environment in %VENV_DIR% ...
    %PYTHON_EXE% -m venv "%VENV_DIR%"
    if errorlevel 1 (
        echo Failed to create virtual environment. Aborting.
        goto :end
    )
)

rem 3) Install/Update Python dependencies
if exist "requirements.txt" (
    echo Installing/Updating Python dependencies...
    "%VENV_PY%" -m pip install --upgrade pip
    if errorlevel 1 (
        echo Warning: Failed to upgrade pip. Continuing...
    )
    "%VENV_PY%" -m pip install -r "requirements.txt"
    if errorlevel 1 (
        echo Failed to install dependencies.
        echo You can try running: "%VENV_PY%" setup.py
        goto :end
    )
)

rem 4) Run preflight checks
echo.
echo Running preflight checks...
"%VENV_PY%" "test_installation.py"
if errorlevel 1 (
    echo.
    echo Some dependencies are missing.
    echo - If Poppler is missing, please follow the instructions in README.md
    echo.
    echo You can also run setup: "%VENV_PY%" setup.py
    goto :end
)

rem 5) Launch the application
echo.
echo Launching application...
"%VENV_PY%" "launcher.py"
set "EXIT_CODE=%ERRORLEVEL%"

echo.
if "%EXIT_CODE%"=="0" (
    echo Application exited successfully.
) else (
    echo Application exited with code %EXIT_CODE%.
)

:end
echo.
pause