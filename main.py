#!/usr/bin/env python3
"""
Optimized PDF to Markdown Converter with performance improvements.
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Tuple
import tkinter as tk
from tkinter import filedialog, ttk, scrolledtext, messagebox
import threading
from queue import Queue
from datetime import datetime
import config
from secure_storage import SecureStorage
from optimizations import OptimizedPDFProcessor, PerformanceMonitor

class OptimizedPDFConverterGUI:
    """Optimized GUI for PDF to Markdown conversion with performance improvements."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("PDF to Markdown Converter (Optimized) - Claude Sonnet 4")
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT + 100}")  # Slightly taller for new features
        
        self.processor = None
        self.selected_folder = ""
        self.output_folder = ""
        self.pdf_files = []
        self.processing_thread = None
        self.secure_storage = SecureStorage()
        self.performance_monitor = PerformanceMonitor()
        
        # Performance settings
        self.max_workers = tk.IntVar(value=4)
        self.batch_size = tk.IntVar(value=3)
        self.image_quality = tk.IntVar(value=85)
        self.selected_model = tk.StringVar(value=config.DEFAULT_MODEL)
        
        self.setup_ui()
        self.load_saved_api_key()
        
    def setup_ui(self):
        """Setup the optimized user interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Main tab
        self.main_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.main_frame, text="Main")
        
        # Performance tab
        self.perf_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.perf_frame, text="Performance")
        
        self.setup_main_tab()
        self.setup_performance_tab()
        
    def setup_main_tab(self):
        """Setup the main conversion tab."""
        # API Key Frame
        api_frame = ttk.LabelFrame(self.main_frame, text="API Configuration", padding="10")
        api_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(api_frame, text="Anthropic API Key:").grid(row=0, column=0, sticky=tk.W)
        self.api_key_entry = ttk.Entry(api_frame, width=40, show="*")
        self.api_key_entry.grid(row=0, column=1, padx=(10, 0))
        
        # API Key management buttons
        self.save_key_button = ttk.Button(api_frame, text="Save Key", command=self.save_api_key)
        self.save_key_button.grid(row=0, column=2, padx=(5, 0))
        
        self.clear_key_button = ttk.Button(api_frame, text="Clear", command=self.clear_api_key)
        self.clear_key_button.grid(row=0, column=3, padx=(5, 0))
        
        # Status label for API key
        self.api_key_status = ttk.Label(api_frame, text="", foreground="green")
        self.api_key_status.grid(row=1, column=1, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # Model selection
        ttk.Label(api_frame, text="Claude Model:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        
        # Create model dropdown with descriptions
        model_values = []
        self.model_mapping = {}  # Map display names to model IDs
        for model_id, model_info in config.CLAUDE_MODELS.items():
            display_name = f"{model_info['name']} ({model_info['speed']}, {model_info['cost']} cost)"
            model_values.append(display_name)
            self.model_mapping[display_name] = model_id
        
        self.model_dropdown = ttk.Combobox(api_frame, textvariable=self.selected_model, 
                                          values=model_values, state="readonly", width=50)
        # Set default selection
        default_model_info = config.CLAUDE_MODELS[config.DEFAULT_MODEL]
        default_display = f"{default_model_info['name']} ({default_model_info['speed']}, {default_model_info['cost']} cost)"
        self.model_dropdown.set(default_display)
        self.model_dropdown.grid(row=2, column=1, columnspan=2, padx=(10, 0), pady=(10, 0))
        
        # Model description label
        self.model_desc_label = ttk.Label(api_frame, text=default_model_info['description'], 
                                         foreground="blue", font=('TkDefaultFont', 9))
        self.model_desc_label.grid(row=3, column=1, columnspan=2, sticky=tk.W, padx=(10, 0), pady=(2, 0))
        
        # Update description when model changes
        self.model_dropdown.bind('<<ComboboxSelected>>', self.on_model_change)
        
        # Folder Selection Frame
        folder_frame = ttk.LabelFrame(self.main_frame, text="Folder Selection", padding="10")
        folder_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(folder_frame, text="Select Input Folder", command=self.select_input_folder).grid(row=0, column=0)
        self.input_folder_label = ttk.Label(folder_frame, text="No folder selected")
        self.input_folder_label.grid(row=0, column=1, padx=(10, 0))
        
        ttk.Button(folder_frame, text="Select Output Folder", command=self.select_output_folder).grid(row=1, column=0, pady=(10, 0))
        self.output_folder_label = ttk.Label(folder_frame, text="No folder selected")
        self.output_folder_label.grid(row=1, column=1, padx=(10, 0), pady=(10, 0))
        
        # File List Frame
        list_frame = ttk.LabelFrame(self.main_frame, text="PDF Files", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Listbox with scrollbar
        self.file_listbox = tk.Listbox(list_frame, height=8)
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.file_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Progress Frame
        progress_frame = ttk.LabelFrame(self.main_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, text="Ready")
        self.status_label.pack()
        
        # Performance info
        self.perf_info_label = ttk.Label(progress_frame, text="", foreground="blue")
        self.perf_info_label.pack(pady=(5, 0))
        
        # Control Buttons
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(pady=10)
        
        self.start_button = ttk.Button(button_frame, text="Start Processing (Optimized)", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Output Log
        log_frame = ttk.LabelFrame(self.main_frame, text="Output Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=6, width=70)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_performance_tab(self):
        """Setup the performance configuration tab."""
        # Performance Settings Frame
        settings_frame = ttk.LabelFrame(self.perf_frame, text="Performance Settings", padding="10")
        settings_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Max Workers
        ttk.Label(settings_frame, text="Max Workers:").grid(row=0, column=0, sticky=tk.W, pady=5)
        workers_spinbox = ttk.Spinbox(settings_frame, from_=1, to=8, textvariable=self.max_workers, width=10)
        workers_spinbox.grid(row=0, column=1, padx=(10, 0), pady=5)
        ttk.Label(settings_frame, text="(Parallel processing threads)").grid(row=0, column=2, padx=(10, 0), pady=5)
        
        # Batch Size
        ttk.Label(settings_frame, text="Batch Size:").grid(row=1, column=0, sticky=tk.W, pady=5)
        batch_spinbox = ttk.Spinbox(settings_frame, from_=1, to=10, textvariable=self.batch_size, width=10)
        batch_spinbox.grid(row=1, column=1, padx=(10, 0), pady=5)
        ttk.Label(settings_frame, text="(Pages processed simultaneously)").grid(row=1, column=2, padx=(10, 0), pady=5)
        
        # Image Quality
        ttk.Label(settings_frame, text="Image Quality:").grid(row=2, column=0, sticky=tk.W, pady=5)
        quality_spinbox = ttk.Spinbox(settings_frame, from_=50, to=100, textvariable=self.image_quality, width=10)
        quality_spinbox.grid(row=2, column=1, padx=(10, 0), pady=5)
        ttk.Label(settings_frame, text="(JPEG quality: lower = faster)").grid(row=2, column=2, padx=(10, 0), pady=5)
        
        # Note about optimizations
        ttk.Label(settings_frame, text="Note: Optimizations include parallel processing and async operations",
                 foreground="blue").grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # Performance Statistics Frame
        stats_frame = ttk.LabelFrame(self.perf_frame, text="Performance Statistics", padding="10")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=15, width=70)
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
        # Clear stats button
        ttk.Button(stats_frame, text="Clear Statistics", command=self.clear_stats).pack(pady=(10, 0))
        
    def select_input_folder(self):
        """Select the input folder containing PDF files."""
        folder = filedialog.askdirectory(title="Select folder containing PDF files")
        if folder:
            self.selected_folder = folder
            self.input_folder_label.config(text=folder)
            self.load_pdf_files()
            
    def select_output_folder(self):
        """Select the output folder for Markdown files."""
        folder = filedialog.askdirectory(title="Select output folder for Markdown files")
        if folder:
            self.output_folder = folder
            self.output_folder_label.config(text=folder)
            
    def load_pdf_files(self):
        """Load PDF files from the selected folder."""
        self.pdf_files = []
        self.file_listbox.delete(0, tk.END)
        
        if self.selected_folder:
            try:
                for ext in config.SUPPORTED_EXTENSIONS:
                    for file in Path(self.selected_folder).glob(f"*{ext}"):
                        self.pdf_files.append(str(file))
                        self.file_listbox.insert(tk.END, file.name)
                        
                self.log(f"Found {len(self.pdf_files)} PDF files")
                
                if len(self.pdf_files) == 0:
                    self.log("No PDF files found in the selected folder")
            except Exception as e:
                self.log(f"Error scanning folder: {e}")
                messagebox.showerror("Error", f"Error scanning folder: {e}")
                
    def log(self, message: str):
        """Add message to the log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def update_performance_info(self, info: str):
        """Update performance information display."""
        self.perf_info_label.config(text=info)
        self.root.update_idletasks()
        
    def start_processing(self):
        """Start optimized processing of PDF files."""
        # Validate inputs
        api_key = self.api_key_entry.get().strip()
        if not api_key:
            self.log("Error: Please enter your Anthropic API key")
            return
            
        if not self.selected_folder:
            self.log("Error: Please select an input folder")
            return
            
        if not self.output_folder:
            self.log("Error: Please select an output folder")
            return
            
        if not self.pdf_files:
            self.log("Error: No PDF files found in the selected folder")
            return
            
        # Initialize optimized processor
        try:
            # Get the actual model ID from the display name
            selected_display = self.model_dropdown.get()
            model_id = self.model_mapping.get(selected_display, config.DEFAULT_MODEL)
            
            self.processor = OptimizedPDFProcessor(
                api_key=api_key,
                max_workers=self.max_workers.get(),
                batch_size=self.batch_size.get(),
                model=model_id
            )
                
        except Exception as e:
            self.log(f"Error initializing processor: {e}")
            messagebox.showerror("Error", f"Error initializing processor: {e}")
            return
        
        # Update UI
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar['maximum'] = len(self.pdf_files)
        self.progress_bar['value'] = 0
        
        # Clear stats for new batch
        self.processor.clear_stats()

        # Start processing in a separate thread
        self.is_processing = True
        self.processing_thread = threading.Thread(target=self.process_files_optimized)
        self.processing_thread.start()
        
    def process_files_optimized(self):
        """Process all PDF files using optimized methods."""
        start_time = time.time()
        successful = 0
        failed = 0
        
        for i, pdf_file in enumerate(self.pdf_files):
            if not self.is_processing:
                break
                
            file_name = Path(pdf_file).name
            self.log(f"Processing: {file_name}")
            self.status_label.config(text=f"Processing: {file_name}")
            
            # Performance monitoring
            timer_id = self.performance_monitor.start_timer(f"process_pdf_{file_name}")
            
            def progress_callback(progress):
                # This will be called from the processor with progress updates
                pass
            
            def status_callback(status):
                self.status_label.config(text=status)
                self.root.update_idletasks()
            
            success, message = self.processor.process_pdf_optimized(
                pdf_file, 
                self.output_folder,
                progress_callback=progress_callback,
                status_callback=status_callback
            )
            
            self.performance_monitor.end_timer(timer_id)
            
            if success:
                self.log(f"✓ {message}")
                successful += 1
            else:
                self.log(f"✗ {message}")
                failed += 1
                
            self.progress_bar['value'] = i + 1
            
            # Update performance info
            stats = self.processor.get_performance_stats()
            if stats.get('pages_per_second'):
                perf_info = f"Speed: {stats['pages_per_second']:.1f} pages/sec | API calls: {stats['api_calls']}"
                self.update_performance_info(perf_info)
            
            self.root.update_idletasks()
        
        # Final statistics
        total_time = time.time() - start_time
        self.status_label.config(text="Processing complete!")
        self.log(f"Processing complete! Successful: {successful}, Failed: {failed}")
        self.log(f"Total time: {total_time:.1f}s")
        
        # Update performance statistics
        self.update_performance_stats()
        
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.is_processing = False
        
    def update_performance_stats(self):
        """Update the performance statistics display."""
        if self.processor:
            stats = self.processor.get_performance_stats()
            monitor_stats = self.performance_monitor.get_summary()
            
            stats_text = "=== PROCESSOR STATISTICS ===\n"
            stats_text += f"Total Pages: {stats.get('total_pages', 0)}\n"
            stats_text += f"Processing Time: {stats.get('processing_time', 0):.2f}s\n"
            stats_text += f"API Calls: {stats.get('api_calls', 0)}\n"
            
            if stats.get('pages_per_second'):
                stats_text += f"Pages per Second: {stats['pages_per_second']:.2f}\n"
                stats_text += f"Avg Time per Page: {stats['avg_time_per_page']:.2f}s\n"
            
            stats_text += "\n=== OPERATION TIMINGS ===\n"
            for operation, timing in monitor_stats.items():
                stats_text += f"{operation}:\n"
                stats_text += f"  Count: {timing['count']}\n"
                stats_text += f"  Total: {timing['total_time']:.2f}s\n"
                stats_text += f"  Average: {timing['avg_time']:.2f}s\n"
                stats_text += f"  Min: {timing['min_time']:.2f}s\n"
                stats_text += f"  Max: {timing['max_time']:.2f}s\n\n"
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, stats_text)
        
    def clear_stats(self):
        """Clear performance statistics."""
        self.performance_monitor = PerformanceMonitor()
        if self.processor:
            self.processor.clear_stats()
        self.stats_text.delete(1.0, tk.END)
        self.update_performance_info("")
        
    def stop_processing(self):
        """Stop the processing."""
        self.is_processing = False
        self.status_label.config(text="Processing stopped")
        self.log("Processing stopped by user")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def load_saved_api_key(self):
        """Load saved API key if available."""
        try:
            if self.secure_storage.has_stored_key():
                api_key = self.secure_storage.load_api_key()
                if api_key:
                    self.api_key_entry.delete(0, tk.END)
                    self.api_key_entry.insert(0, api_key)
                    self.api_key_status.config(text="✓ API key loaded from secure storage", foreground="green")
                    self.log("API key loaded from secure storage")
        except Exception as e:
            self.log(f"Error loading saved API key: {e}")
    
    def save_api_key(self):
        """Save the current API key securely."""
        api_key = self.api_key_entry.get().strip()
        if not api_key:
            messagebox.showwarning("Warning", "Please enter an API key first")
            return
        
        try:
            if self.secure_storage.save_api_key(api_key):
                self.api_key_status.config(text="✓ API key saved securely", foreground="green")
                self.log("API key saved securely")
                messagebox.showinfo("Success", "API key saved securely!")
            else:
                self.api_key_status.config(text="✗ Failed to save API key", foreground="red")
                messagebox.showerror("Error", "Failed to save API key")
        except Exception as e:
            self.log(f"Error saving API key: {e}")
            messagebox.showerror("Error", f"Error saving API key: {e}")
    
    def clear_api_key(self):
        """Clear the API key from both UI and storage."""
        result = messagebox.askyesno("Confirm", "Clear saved API key from secure storage?")
        if result:
            try:
                self.secure_storage.delete_api_key()
                self.api_key_entry.delete(0, tk.END)
                self.api_key_status.config(text="API key cleared", foreground="gray")
                self.log("API key cleared from storage")
                messagebox.showinfo("Success", "API key cleared from secure storage")
            except Exception as e:
                self.log(f"Error clearing API key: {e}")
                messagebox.showerror("Error", f"Error clearing API key: {e}")
    
    def on_model_change(self, event=None):
        """Handle model selection change."""
        selected_display = self.model_dropdown.get()
        model_id = self.model_mapping.get(selected_display, config.DEFAULT_MODEL)
        model_info = config.CLAUDE_MODELS.get(model_id, {})
        self.model_desc_label.config(text=model_info.get('description', ''))
        self.log(f"Selected model: {model_info.get('name', 'Unknown')}")
        
        # Update performance recommendation based on model
        if 'haiku' in model_id.lower():
            self.log("Tip: Haiku models are fastest and most cost-effective for simple documents")
        elif 'opus' in model_id.lower():
            self.log("Tip: Opus provides best quality for complex documents with tables and formulas")
        elif 'sonnet' in model_id.lower():
            self.log("Tip: Sonnet models offer balanced performance and quality")

def main():
    """Main function to run the optimized application."""
    root = tk.Tk()
    app = OptimizedPDFConverterGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
