"""
Authenticated Scraper for Niagara/Tridium Documentation
This version allows manual login and then scrapes authenticated content
"""

import os
import re
import json
import time
import logging
from pathlib import Path
from urllib.parse import urljoin, urlparse
from typing import List, Dict

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AuthenticatedScraper:
    """Scraper that allows manual authentication before scraping"""
    
    def __init__(self, download_dir: str = "authenticated_documents"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.driver = None
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def setup_browser(self, headless: bool = False):
        """Setup browser for manual login"""
        logger.info("Setting up browser...")
        options = Options()
        if headless:
            options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--window-size=1920,1080')
        
        self.driver = webdriver.Chrome(options=options)
        return self.driver
    
    def manual_login(self, login_url: str):
        """
        Opens the browser to allow manual login
        """
        if not self.driver:
            self.setup_browser(headless=False)  # Can't be headless for manual login
        
        logger.info(f"Opening {login_url}")
        self.driver.get(login_url)
        
        print("\n" + "="*60)
        print("MANUAL LOGIN REQUIRED")
        print("="*60)
        print("Please log in manually in the browser window that just opened.")
        print("After logging in successfully, press ENTER here to continue...")
        print("="*60)
        
        input("\nPress ENTER after you've logged in successfully...")
        
        # Get cookies from Selenium session
        cookies = self.driver.get_cookies()
        
        # Transfer cookies to requests session
        for cookie in cookies:
            self.session.cookies.set(cookie['name'], cookie['value'])
        
        logger.info("Login complete, cookies saved")
        return True
    
    def scrape_authenticated_content(self, target_urls: List[str]) -> List[Dict]:
        """Scrape content after authentication"""
        all_documents = []
        
        for url in target_urls:
            logger.info(f"Scraping authenticated content from {url}")
            
            try:
                # Navigate to the page
                self.driver.get(url)
                
                # Wait for content to load
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                time.sleep(3)  # Additional wait for dynamic content
                
                # Look for all document links
                documents = self.extract_documents_from_page()
                all_documents.extend(documents)
                
                # Try to navigate through pages or load more content
                self.handle_pagination()
                
                # Extract again after pagination
                more_docs = self.extract_documents_from_page()
                all_documents.extend(more_docs)
                
            except Exception as e:
                logger.error(f"Error scraping {url}: {e}")
        
        # Remove duplicates
        seen = set()
        unique_docs = []
        for doc in all_documents:
            doc_id = doc['url']
            if doc_id not in seen:
                seen.add(doc_id)
                unique_docs.append(doc)
        
        return unique_docs
    
    def extract_documents_from_page(self) -> List[Dict]:
        """Extract all documents from the current page"""
        documents = []
        
        # Multiple strategies to find documents
        selectors = [
            "a[href*='.pdf']",
            "a[href*='.doc']",
            "a[href*='.docx']",
            "a[href*='.xls']",
            "a[href*='.xlsx']",
            "a[href*='.zip']",
            "a[href*='download']",
            ".document-link",
            ".resource-link",
            "[data-document]",
            ".file-item"
        ]
        
        for selector in selectors:
            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
            
            for elem in elements:
                try:
                    href = elem.get_attribute('href')
                    if not href:
                        continue
                    
                    title = (elem.text or 
                            elem.get_attribute('title') or 
                            elem.get_attribute('aria-label') or
                            os.path.basename(urlparse(href).path))
                    
                    # Get additional metadata
                    parent = elem.find_element(By.XPATH, "./..")
                    description = parent.text if parent else ""
                    
                    doc = {
                        'title': title.strip(),
                        'url': href,
                        'description': description[:200],
                        'type': self.determine_doc_type(href, title),
                        'category': self.categorize_document(title)
                    }
                    
                    documents.append(doc)
                    
                except Exception as e:
                    logger.debug(f"Error extracting document: {e}")
        
        logger.info(f"Found {len(documents)} documents on current page")
        return documents
    
    def handle_pagination(self):
        """Handle pagination or 'load more' buttons"""
        try:
            # Look for pagination controls
            pagination_selectors = [
                "button:contains('Load More')",
                "button:contains('Show More')",
                "a:contains('Next')",
                ".pagination a",
                "[aria-label='Next page']"
            ]
            
            for selector in pagination_selectors:
                try:
                    # Try XPath for text content
                    elements = self.driver.find_elements(By.XPATH, 
                        f"//*[contains(text(), 'Load More') or contains(text(), 'Show More') or contains(text(), 'Next')]")
                    
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            self.driver.execute_script("arguments[0].scrollIntoView();", elem)
                            elem.click()
                            time.sleep(3)
                            logger.info("Clicked pagination control")
                            break
                except:
                    pass
            
            # Also try scrolling to load more content
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            
            if new_height > last_height:
                logger.info("Scrolled to load more content")
                
        except Exception as e:
            logger.debug(f"No pagination found or error: {e}")
    
    def determine_doc_type(self, url: str, title: str) -> str:
        """Determine document type"""
        url_lower = url.lower()
        
        if '.pdf' in url_lower:
            return 'PDF'
        elif any(ext in url_lower for ext in ['.doc', '.docx']):
            return 'Word'
        elif any(ext in url_lower for ext in ['.xls', '.xlsx']):
            return 'Excel'
        elif any(ext in url_lower for ext in ['.ppt', '.pptx']):
            return 'PowerPoint'
        elif '.zip' in url_lower:
            return 'Archive'
        else:
            return 'Document'
    
    def categorize_document(self, title: str) -> str:
        """Categorize based on title"""
        title_lower = title.lower()
        
        if any(word in title_lower for word in ['manual', 'guide', 'installation']):
            return 'manuals'
        elif any(word in title_lower for word in ['datasheet', 'brochure', 'catalog']):
            return 'sales'
        elif any(word in title_lower for word in ['bulletin', 'technical', 'spec']):
            return 'technical'
        elif any(word in title_lower for word in ['training', 'tutorial', 'course']):
            return 'training'
        elif any(word in title_lower for word in ['release', 'notes', 'update']):
            return 'release_notes'
        else:
            return 'general'
    
    def download_document(self, doc: Dict) -> bool:
        """Download a document using the authenticated session"""
        try:
            category_dir = self.download_dir / doc['category']
            category_dir.mkdir(exist_ok=True)
            
            # Clean filename
            filename = re.sub(r'[<>:"/\\|?*]', '_', doc['title'])[:200]
            if not any(filename.endswith(ext) for ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip']):
                ext_map = {'PDF': '.pdf', 'Word': '.docx', 'Excel': '.xlsx'}
                filename += ext_map.get(doc['type'], '.pdf')
            
            filepath = category_dir / filename
            
            if filepath.exists():
                logger.info(f"Already exists: {filename}")
                return True
            
            logger.info(f"Downloading: {doc['title']}")
            
            # Use cookies from browser session
            cookies = self.driver.get_cookies()
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])
            
            response = self.session.get(doc['url'], stream=True, timeout=30)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info(f"Saved to: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to download {doc['title']}: {e}")
            return False
    
    def run(self, login_url: str, target_urls: List[str], download: bool = True):
        """Main execution flow"""
        print("\n" + "="*60)
        print("AUTHENTICATED DOCUMENT SCRAPER")
        print("="*60)
        
        # Step 1: Manual login
        self.manual_login(login_url)
        
        # Step 2: Scrape authenticated content
        documents = self.scrape_authenticated_content(target_urls)
        
        # Step 3: Display summary
        print("\n" + "="*60)
        print("DOCUMENTS FOUND")
        print("="*60)
        
        categorized = {}
        for doc in documents:
            cat = doc['category']
            if cat not in categorized:
                categorized[cat] = []
            categorized[cat].append(doc)
        
        for category, docs in categorized.items():
            print(f"\n{category.upper()}: {len(docs)} documents")
            for doc in docs[:3]:
                print(f"  - {doc['title'][:60]}...")
            if len(docs) > 3:
                print(f"  ... and {len(docs)-3} more")
        
        print(f"\nTotal documents found: {len(documents)}")
        
        # Save metadata
        metadata_file = self.download_dir / "metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump({
                'total': len(documents),
                'categories': categorized
            }, f, indent=2)
        
        # Step 4: Download if requested
        if download and documents:
            print("\n" + "="*60)
            print("DOWNLOADING DOCUMENTS")
            print("="*60)
            
            success = 0
            failed = 0
            
            for i, doc in enumerate(documents, 1):
                print(f"[{i}/{len(documents)}] {doc['title'][:60]}...")
                
                if self.download_document(doc):
                    success += 1
                else:
                    failed += 1
                
                time.sleep(0.5)  # Rate limiting
            
            print(f"\nDownload complete: {success} successful, {failed} failed")
        
        # Cleanup
        if self.driver:
            self.driver.quit()

def main():
    """Main function with common Niagara/Tridium login URLs"""
    
    # Common login URLs for Niagara/Tridium sites
    LOGIN_URLS = {
        'niagara_central': 'https://www.niagara-central.com/login',
        'niagara_community': 'https://docs.niagara-community.com/login',
        'tridium': 'https://www.tridium.com/login'
    }
    
    # Target URLs to scrape after login
    TARGET_URLS = [
        'https://docs.niagara-community.com/category/reseller',
        'https://docs.niagara-community.com/category/tech_bull',
        'https://www.niagara-central.com/documents',
        'https://www.tridium.com/us/en/services-support/library'
    ]
    
    print("\nAvailable login sites:")
    for key, url in LOGIN_URLS.items():
        print(f"  {key}: {url}")
    
    site_choice = input("\nWhich site do you want to log into? (or enter custom URL): ").strip()
    
    if site_choice in LOGIN_URLS:
        login_url = LOGIN_URLS[site_choice]
    else:
        login_url = site_choice
    
    scraper = AuthenticatedScraper()
    
    # Run the scraper
    import sys
    download = '--no-download' not in sys.argv
    scraper.run(login_url, TARGET_URLS, download=download)

if __name__ == "__main__":
    main()